{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/Hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\n\nexport default function Hero() {\n  return (\n    <section className=\"hero\">\n      <div className=\"container\">\n        <motion.div\n          className=\"hero-content\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <div className=\"hero-badge\">AUTO PILOT ON SOCIAL MEDIA</div>\n          <h1 className=\"hero-title\">\n            Go viral — just by being you. Automatically.\n            <br />\n            <span className=\"gradient-text\">\n              PersonaRoll is always on trend\n            </span>\n          </h1>\n          <p className=\"hero-subtitle\">\n            Upload your photos, let AI match them with trending topics, then\n            watch as your personas create authentic content in their unique\n            voices - all powered by real knowledge from your feeds and sources.\n          </p>\n          <div className=\"hero-actions\">\n            <button className=\"btn-primary btn-large\">Get Early Access</button>\n            <button className=\"btn-secondary btn-large\">Watch Demo</button>\n          </div>\n          <div className=\"hero-stats\">\n            <div className=\"stat\">\n              <span className=\"stat-number\">AI</span>\n              <span className=\"stat-label\">Personality creation</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">Live</span>\n              <span className=\"stat-label\">Social feed analysis</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">∞</span>\n              <span className=\"stat-label\">Memory retention</span>\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hero-visual\">\n          <div className=\"hero-mockup\">\n            <motion.div\n              className=\"floating-card card-1\"\n              animate={{\n                y: [0, -20, 0],\n                rotate: [0, 2, 0],\n              }}\n              transition={{\n                duration: 6,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n              }}\n            >\n              <div className=\"persona-preview\">\n                <img\n                  src=\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='30' cy='30' r='30' fill='url(%23gradient1)'/%3E%3Cdefs%3E%3ClinearGradient id='gradient1' x1='0' y1='0' x2='60' y2='60'%3E%3Cstop stop-color='%23ff2e4d'/%3E%3Cstop offset='1' stop-color='%237c3aed'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E\"\n                  alt=\"Persona\"\n                />\n                <div>\n                  <h4>Fashion Qianqian</h4>\n                  <p>ENFP • Trendy Voice</p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              className=\"floating-card card-2\"\n              animate={{\n                y: [0, -15, 0],\n                rotate: [0, -1, 0],\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n                delay: 2,\n              }}\n            >\n              <div className=\"platform-icons\">\n                <div className=\"source-icon\">\n                  <svg\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                  >\n                    <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\" />\n                    <polyline points=\"22,6 12,13 2,6\" />\n                  </svg>\n                </div>\n                <div className=\"source-icon\">\n                  <svg\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                  >\n                    <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\n                    <polyline points=\"14,2 14,8 20,8\" />\n                    <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\n                    <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\n                    <polyline points=\"10,9 9,9 8,9\" />\n                  </svg>\n                </div>\n                <div className=\"source-icon\">\n                  <svg\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                  >\n                    <path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\" />\n                    <path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\" />\n                  </svg>\n                </div>\n                <div className=\"source-icon\">\n                  <svg\n                    width=\"24\"\n                    height=\"24\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                  >\n                    <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n                    <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" />\n                    <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" />\n                    <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" />\n                    <path d=\"M8 14h.01\" />\n                    <path d=\"M12 14h.01\" />\n                    <path d=\"M16 14h.01\" />\n                    <path d=\"M8 18h.01\" />\n                    <path d=\"M12 18h.01\" />\n                  </svg>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              className=\"floating-card card-3\"\n              animate={{\n                y: [0, -25, 0],\n                rotate: [0, 1, 0],\n              }}\n              transition={{\n                duration: 7,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n                delay: 4,\n              }}\n            >\n              <div className=\"content-preview\">\n                <div className=\"preview-image\">\n                  <svg width=\"80\" height=\"60\" viewBox=\"0 0 80 60\" fill=\"none\">\n                    <defs>\n                      <linearGradient\n                        id=\"autumnBg\"\n                        x1=\"0%\"\n                        y1=\"0%\"\n                        x2=\"100%\"\n                        y2=\"100%\"\n                      >\n                        <stop offset=\"0%\" style={{ stopColor: \"#FFA07A\" }} />\n                        <stop offset=\"50%\" style={{ stopColor: \"#CD853F\" }} />\n                        <stop offset=\"100%\" style={{ stopColor: \"#8B4513\" }} />\n                      </linearGradient>\n                      <linearGradient\n                        id=\"sweaterGrad\"\n                        x1=\"0%\"\n                        y1=\"0%\"\n                        x2=\"100%\"\n                        y2=\"100%\"\n                      >\n                        <stop offset=\"0%\" style={{ stopColor: \"#D2691E\" }} />\n                        <stop offset=\"100%\" style={{ stopColor: \"#A0522D\" }} />\n                      </linearGradient>\n                    </defs>\n                    <rect width=\"80\" height=\"60\" fill=\"url(#autumnBg)\" rx=\"6\" />\n\n                    <g opacity=\"0.3\">\n                      <path\n                        d=\"M15 12 Q18 8 21 12 Q18 16 15 12\"\n                        fill=\"#8B0000\"\n                      />\n                      <path\n                        d=\"M62 15 Q65 11 68 15 Q65 19 62 15\"\n                        fill=\"#FF6347\"\n                      />\n                      <path d=\"M8 35 Q11 31 14 35 Q11 39 8 35\" fill=\"#CD853F\" />\n                      <path\n                        d=\"M70 45 Q73 41 76 45 Q73 49 70 45\"\n                        fill=\"#B22222\"\n                      />\n                      <path\n                        d=\"M25 50 Q28 46 31 50 Q28 54 25 50\"\n                        fill=\"#FF8C00\"\n                      />\n                    </g>\n\n                    <ellipse\n                      cx=\"40\"\n                      cy=\"35\"\n                      rx=\"18\"\n                      ry=\"20\"\n                      fill=\"url(#sweaterGrad)\"\n                    />\n                    <ellipse cx=\"40\" cy=\"32\" rx=\"15\" ry=\"16\" fill=\"#DEB887\" />\n\n                    <line\n                      x1=\"30\"\n                      y1=\"25\"\n                      x2=\"50\"\n                      y2=\"25\"\n                      stroke=\"#CD853F\"\n                      strokeWidth=\"0.5\"\n                    />\n                    <line\n                      x1=\"30\"\n                      y1=\"30\"\n                      x2=\"50\"\n                      y2=\"30\"\n                      stroke=\"#CD853F\"\n                      strokeWidth=\"0.5\"\n                    />\n                    <line\n                      x1=\"30\"\n                      y1=\"35\"\n                      x2=\"50\"\n                      y2=\"35\"\n                      stroke=\"#CD853F\"\n                      strokeWidth=\"0.5\"\n                    />\n\n                    <ellipse cx=\"40\" cy=\"18\" rx=\"12\" ry=\"4\" fill=\"#8B0000\" />\n                    <rect x=\"48\" y=\"15\" width=\"3\" height=\"12\" fill=\"#8B0000\" />\n                    <rect x=\"52\" y=\"17\" width=\"2\" height=\"8\" fill=\"#CD853F\" />\n\n                    <rect\n                      x=\"32\"\n                      y=\"48\"\n                      width=\"16\"\n                      height=\"10\"\n                      fill=\"#4682B4\"\n                      rx=\"2\"\n                    />\n                    <line\n                      x1=\"34\"\n                      y1=\"50\"\n                      x2=\"34\"\n                      y2=\"56\"\n                      stroke=\"#191970\"\n                      strokeWidth=\"0.5\"\n                    />\n                    <line\n                      x1=\"46\"\n                      y1=\"50\"\n                      x2=\"46\"\n                      y2=\"56\"\n                      stroke=\"#191970\"\n                      strokeWidth=\"0.5\"\n                    />\n\n                    <ellipse cx=\"36\" cy=\"57\" rx=\"4\" ry=\"2\" fill=\"#654321\" />\n                    <ellipse cx=\"44\" cy=\"57\" rx=\"4\" ry=\"2\" fill=\"#654321\" />\n\n                    <circle\n                      cx=\"20\"\n                      cy=\"20\"\n                      r=\"1\"\n                      fill=\"#FFD700\"\n                      opacity=\"0.8\"\n                    />\n                    <circle\n                      cx=\"60\"\n                      cy=\"25\"\n                      r=\"1\"\n                      fill=\"#FFD700\"\n                      opacity=\"0.8\"\n                    />\n                    <circle\n                      cx=\"15\"\n                      cy=\"45\"\n                      r=\"1\"\n                      fill=\"#FFD700\"\n                      opacity=\"0.6\"\n                    />\n                    <circle\n                      cx=\"65\"\n                      cy=\"50\"\n                      r=\"1\"\n                      fill=\"#FFD700\"\n                      opacity=\"0.6\"\n                    />\n                  </svg>\n                </div>\n                <p>Just discovered the perfect autumn outfit combo! 🍂</p>\n                <span className=\"preview-meta\">\n                  AI Generated • Ready to publish\n                </span>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;sCAAa;;;;;;sCAC5B,8OAAC;4BAAG,WAAU;;gCAAa;8CAEzB,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAIlC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAwB;;;;;;8CAC1C,8OAAC;oCAAO,WAAU;8CAA0B;;;;;;;;;;;;sCAE9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;;;;;;;;;;;;;8BAKnC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,QAAQ;wCAAC;wCAAG;wCAAG;qCAAE;gCACnB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;0CAEA,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAI;4CACJ,KAAI;;;;;;sDAEN,8OAAC;;8DACC,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;0CAKT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,QAAQ;wCAAC;wCAAG,CAAC;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;oCACN,OAAO;gCACT;0CAEA,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;;kEAEZ,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAS,QAAO;;;;;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;;kEAEZ,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAS,QAAO;;;;;;kEACjB,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEAChC,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEAChC,8OAAC;wDAAS,QAAO;;;;;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;;kEAEZ,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;;kEAEZ,8OAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEACnD,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;;;;;;kEAChC,8OAAC;wDAAK,IAAG;wDAAI,IAAG;wDAAI,IAAG;wDAAI,IAAG;;;;;;kEAC9B,8OAAC;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;kEAChC,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,QAAQ;wCAAC;wCAAG;wCAAG;qCAAE;gCACnB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;oCACN,OAAO;gCACT;0CAEA,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;;kEACnD,8OAAC;;0EACC,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAM,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFACjD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;;;;;;;kEAGtD,8OAAC;wDAAK,OAAM;wDAAK,QAAO;wDAAK,MAAK;wDAAiB,IAAG;;;;;;kEAEtD,8OAAC;wDAAE,SAAQ;;0EACT,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEAAK,GAAE;gEAAiC,MAAK;;;;;;0EAC9C,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;;;;;;;kEAIT,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,MAAK;;;;;;kEAEP,8OAAC;wDAAQ,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,MAAK;;;;;;kEAE9C,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,QAAO;wDACP,aAAY;;;;;;kEAEd,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,QAAO;wDACP,aAAY;;;;;;kEAEd,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,QAAO;wDACP,aAAY;;;;;;kEAGd,8OAAC;wDAAQ,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAI,MAAK;;;;;;kEAC7C,8OAAC;wDAAK,GAAE;wDAAK,GAAE;wDAAK,OAAM;wDAAI,QAAO;wDAAK,MAAK;;;;;;kEAC/C,8OAAC;wDAAK,GAAE;wDAAK,GAAE;wDAAK,OAAM;wDAAI,QAAO;wDAAI,MAAK;;;;;;kEAE9C,8OAAC;wDACC,GAAE;wDACF,GAAE;wDACF,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,IAAG;;;;;;kEAEL,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,QAAO;wDACP,aAAY;;;;;;kEAEd,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,IAAG;wDACH,QAAO;wDACP,aAAY;;;;;;kEAGd,8OAAC;wDAAQ,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;wDAAI,MAAK;;;;;;kEAC5C,8OAAC;wDAAQ,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;wDAAI,MAAK;;;;;;kEAE5C,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,GAAE;wDACF,MAAK;wDACL,SAAQ;;;;;;kEAEV,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,GAAE;wDACF,MAAK;wDACL,SAAQ;;;;;;kEAEV,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,GAAE;wDACF,MAAK;wDACL,SAAQ;;;;;;kEAEV,8OAAC;wDACC,IAAG;wDACH,IAAG;wDACH,GAAE;wDACF,MAAK;wDACL,SAAQ;;;;;;;;;;;;;;;;;sDAId,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\n\nexport default function FeaturesSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.25, 0.1, 0.35, 1] as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"features\" ref={ref}>\n      <div className=\"container\">\n        <div className=\"section-header\">\n          <h2 className=\"section-title\">\n            The Complete <span className=\"gradient-text\">Photo-to-Post</span>{\" \"}\n            Workflow\n          </h2>\n          <p className=\"section-subtitle\">\n            From your camera roll to viral content in three simple steps\n          </p>\n        </div>\n\n        <motion.div\n          className=\"features-grid\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Camera Roll Feature - PRIMARY */}\n          <motion.div\n            className=\"feature-card featured primary\"\n            variants={cardVariants}\n          >\n            <div className=\"feature-icon\">\n              <div className=\"icon-camera\">\n                <div className=\"camera-lens\"></div>\n                <div className=\"camera-flash\"></div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Step 1: Upload Your Photos</h3>\n            <p className=\"feature-description\">\n              Start with any photo from your camera roll. Our AI instantly\n              analyzes the image and matches it with current trending topics\n              across social platforms.\n            </p>\n            <ul className=\"feature-list\">\n              <li>Upload photos from any moment</li>\n              <li>AI matches photos to current trends</li>\n              <li>Your visuals drive the content strategy</li>\n            </ul>\n          </motion.div>\n\n          {/* Persona Feature */}\n          <motion.div className=\"feature-card featured\" variants={cardVariants}>\n            <div className=\"feature-icon\">\n              <div className=\"icon-personas\">\n                <div className=\"mini-persona p1\"></div>\n                <div className=\"mini-persona p2\"></div>\n                <div className=\"mini-persona p3\"></div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Step 2: Choose Your Voice</h3>\n            <p className=\"feature-description\">\n              Select which persona will write your content. Each has their own\n              personality, reads different sources, and writes in a unique style\n              that stays consistent across all posts.\n            </p>\n            <ul className=\"feature-list\">\n              <li>Multiple AI personalities to choose from</li>\n              <li>Each reads custom knowledge sources</li>\n              <li>Maintains consistent voice and tone</li>\n            </ul>\n          </motion.div>\n\n          {/* Trend Detection */}\n          <motion.div className=\"feature-card\" variants={cardVariants}>\n            <div className=\"feature-icon\">\n              <div className=\"icon-trend\">\n                <div className=\"trend-line line-1\"></div>\n                <div className=\"trend-line line-2\"></div>\n                <div className=\"trend-line line-3\"></div>\n                <div className=\"trend-dot dot-1\"></div>\n                <div className=\"trend-dot dot-2\"></div>\n                <div className=\"trend-dot dot-3\"></div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Step 3: Get Your Post</h3>\n            <p className=\"feature-description\">\n              Watch as AI combines your photo, trending topics, and persona\n              knowledge to create authentic content that sounds exactly like you\n              would write it.\n            </p>\n          </motion.div>\n\n          {/* AI Knowledge Integration */}\n          <motion.div className=\"feature-card\" variants={cardVariants}>\n            <div className=\"feature-icon\">\n              <div className=\"icon-knowledge\">\n                <div className=\"knowledge-sources\">\n                  <div className=\"source-node doc\"></div>\n                  <div className=\"source-node link\"></div>\n                  <div className=\"source-node feed\"></div>\n                  <div className=\"knowledge-center\"></div>\n                  <div className=\"knowledge-flow flow-1\"></div>\n                  <div className=\"knowledge-flow flow-2\"></div>\n                  <div className=\"knowledge-flow flow-3\"></div>\n                </div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Smart Knowledge Sources</h3>\n            <p className=\"feature-description\">\n              Your personas don't just write - they read. Connect knowledge\n              bases, RSS feeds, and social media sources to keep your content\n              informed and relevant.\n            </p>\n          </motion.div>\n\n          {/* Trending Topics */}\n          <motion.div className=\"feature-card\" variants={cardVariants}>\n            <div className=\"feature-icon\">\n              <div className=\"icon-viral\">\n                <div className=\"viral-bubble bubble-1\"></div>\n                <div className=\"viral-bubble bubble-2\"></div>\n                <div className=\"viral-bubble bubble-3\"></div>\n                <div className=\"viral-pulse\"></div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Always On-Trend</h3>\n            <p className=\"feature-description\">\n              Real-time trend detection ensures your content matches what's hot\n              right now. Never miss a viral moment or trending conversation.\n            </p>\n          </motion.div>\n\n          {/* Analytics */}\n          <motion.div className=\"feature-card\" variants={cardVariants}>\n            <div className=\"feature-icon\">\n              <div className=\"icon-analytics\">\n                <div className=\"chart-bar bar-1\"></div>\n                <div className=\"chart-bar bar-2\"></div>\n                <div className=\"chart-bar bar-3\"></div>\n                <div className=\"chart-bar bar-4\"></div>\n                <div className=\"chart-line\"></div>\n              </div>\n            </div>\n            <h3 className=\"feature-title\">Performance Insights</h3>\n            <p className=\"feature-description\">\n              Track what works across all platforms. See which photos, personas,\n              and topics drive the most engagement to optimize your strategy.\n            </p>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAK;oBAAM;iBAAE;YAC5B;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAW,KAAK;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAgB;8CACf,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAqB;gCAAI;;;;;;;sCAGxE,8OAAC;4BAAE,WAAU;sCAAmB;;;;;;;;;;;;8BAKlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAKnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAwB,UAAU;;8CACtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAKnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAe,UAAU;;8CAC7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAQrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAe,UAAU;;8CAC7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAIrB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAQrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAe,UAAU;;8CAC7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAOrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAe,UAAU;;8CAC7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/ScreenshotsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\n\nexport default function ScreenshotsSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section className=\"screenshots\" ref={ref}>\n      <div className=\"container\">\n        <div className=\"section-header\">\n          <h2 className=\"section-title\">\n            See PersonaRoll <span className=\"gradient-text\">In Action</span>\n          </h2>\n          <p className=\"section-subtitle\">\n            A glimpse into your new content creation workflow\n          </p>\n        </div>\n\n        <motion.div\n          className=\"screenshots-showcase\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Screenshot 1: Photo Upload */}\n          <motion.div className=\"screenshot-item\" variants={itemVariants}>\n            <div className=\"screenshot-image\">\n              <div className=\"demo-camera-roll\">\n                {/* Header with title and filter tabs */}\n                <div className=\"camera-header\">\n                  <div className=\"camera-title-section\">\n                    <h4 className=\"camera-title\">Camera Roll Analyzer</h4>\n                    <span className=\"camera-tip\">\n                      AI analyzes photos for trending content\n                    </span>\n                  </div>\n                  <div className=\"camera-filter-tabs\">\n                    <div className=\"filter-tab active\">All (24)</div>\n                    <div className=\"filter-tab\">Fashion (8)</div>\n                    <div className=\"filter-tab\">Lifestyle (6)</div>\n                    <div className=\"filter-tab\">Food (4)</div>\n                    <div className=\"filter-tab\">Travel (6)</div>\n                  </div>\n                </div>\n\n                {/* Photo Grid */}\n                <div className=\"real-photo-grid\">\n                  <div className=\"real-photo-card selected\">\n                    <div className=\"photo-image-container\">\n                      <div className=\"photo-placeholder fashion\"></div>\n                      <div className=\"delete-button\">🗑️</div>\n                    </div>\n                    <div className=\"photo-info\">\n                      <div className=\"photo-filename\">\n                        autumn_outfit_2024.jpg\n                      </div>\n                      <div className=\"photo-description\">\n                        Cozy autumn sweater with matching accessories, perfect\n                        for fall weather trends\n                      </div>\n                      <div className=\"photo-meta\">\n                        <span className=\"photo-date\">Oct 28, 2024</span>\n                        <span className=\"photo-status used\">Used</span>\n                      </div>\n                      <div className=\"photo-tags\">\n                        <span className=\"tag\">autumn</span>\n                        <span className=\"tag\">fashion</span>\n                        <span className=\"tag\">cozy</span>\n                      </div>\n                      <div className=\"photo-type-badge\">Fashion</div>\n                    </div>\n                  </div>\n\n                  <div className=\"real-photo-card\">\n                    <div className=\"photo-image-container\">\n                      <div className=\"photo-placeholder lifestyle\"></div>\n                    </div>\n                    <div className=\"photo-info\">\n                      <div className=\"photo-filename\">morning_coffee.jpg</div>\n                      <div className=\"photo-description\">\n                        Perfect morning coffee setup with natural lighting\n                      </div>\n                      <div className=\"photo-meta\">\n                        <span className=\"photo-date\">Oct 27, 2024</span>\n                        <span className=\"photo-status unused\">Unused</span>\n                      </div>\n                      <div className=\"photo-tags\">\n                        <span className=\"tag\">coffee</span>\n                        <span className=\"tag\">morning</span>\n                      </div>\n                      <div className=\"photo-type-badge\">Lifestyle</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>Start with Your Photos</h3>\n              <p>\n                Upload any photo and watch AI instantly match it with trending\n                topics. Your visuals drive the content, not the other way\n                around.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 2: Persona Selection */}\n          <motion.div\n            className=\"screenshot-item reverse\"\n            variants={itemVariants}\n          >\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-persona-manager-compact\">\n                  <div className=\"persona-grid\">\n                    <div className=\"persona-card active\">\n                      <div className=\"persona-card-avatar fiona\">F</div>\n                      <h4>Fashion Fiona</h4>\n                      <p>ENFP • Fashion & Lifestyle</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini active\">\n                          Fashion\n                        </div>\n                        <div className=\"knowledge-chip-mini active\">Style</div>\n                        <div className=\"knowledge-chip-mini active\">Trends</div>\n                      </div>\n                    </div>\n\n                    <div className=\"persona-card\">\n                      <div className=\"persona-card-avatar marcus\">M</div>\n                      <h4>Chef Marcus</h4>\n                      <p>ISFJ • Culinary Expert</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini\">Recipes</div>\n                        <div className=\"knowledge-chip-mini\">Cooking</div>\n                        <div className=\"knowledge-chip-mini\">Food</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"persona-detail-summary\">\n                    <h4\n                      style={{\n                        color: \"var(--text-primary)\",\n                        marginBottom: \"12px\",\n                        fontSize: \"16px\",\n                      }}\n                    >\n                      Selected: Fashion Fiona\n                    </h4>\n                    <p\n                      style={{\n                        color: \"var(--text-secondary)\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.5\",\n                        marginBottom: \"16px\",\n                      }}\n                    >\n                      A fashion-forward influencer who stays ahead of trends and\n                      creates authentic style content. Reads fashion feeds and\n                      writes with trendy enthusiasm.\n                    </p>\n                    <div className=\"knowledge-sources-summary\">\n                      <div className=\"knowledge-chips\">\n                        <div className=\"knowledge-chip active\">Vogue RSS</div>\n                        <div className=\"knowledge-chip active\">\n                          Fashion Week\n                        </div>\n                        <div className=\"knowledge-chip active\">Style Blogs</div>\n                        <div className=\"knowledge-chip\">Beauty Tips</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>Personas with Custom Knowledge</h3>\n              <p>\n                Each persona reads different sources and writes in their unique\n                style. Fashion Fiona follows fashion feeds, while Foodie Fred\n                reads culinary blogs.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 3: AI Generation Process */}\n          <motion.div className=\"screenshot-item\" variants={itemVariants}>\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-content-generation-compact\">\n                  <div className=\"generation-process\">\n                    <div className=\"process-steps\">\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📸</div>\n                        <span>Photo Analysis</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📈</div>\n                        <span>Trend Match</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">✨</div>\n                        <span>Generate</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"generated-content-preview\">\n                    <div className=\"content-example\">\n                      <div className=\"content-header\">\n                        <div className=\"persona-badge\">Fashion Fiona</div>\n                        <div className=\"trend-badge\">Autumn Trends</div>\n                      </div>\n                      <div className=\"content-body\">\n                        <p>\n                          \"OMG this autumn look is EVERYTHING! 😍 The oversized\n                          knit + ankle boots combo is absolutely perfect for the\n                          cozy fall vibes we're all craving right now!\"\n                        </p>\n                      </div>\n                      <div className=\"content-platforms\">\n                        <span className=\"platform-icon\">📱</span>\n                        <span className=\"platform-icon\">📷</span>\n                        <span className=\"platform-icon\">🐦</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>Smart Content Generation</h3>\n              <p>\n                AI analyzes your photo, matches it with current trends, then\n                generates content in your chosen persona's unique voice and\n                style.\n              </p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAc,KAAK;kBACpC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAgB;8CACZ,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElD,8OAAC;4BAAE,WAAU;sCAAmB;;;;;;;;;;;;8BAKlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAkB,UAAU;;8CAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe;;;;;;0EAC7B,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAI/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAoB;;;;;;0EACnC,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;0EAAa;;;;;;;;;;;;;;;;;;0DAKhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;;;;;;;0EAEjC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiB;;;;;;kFAGhC,8OAAC;wEAAI,WAAU;kFAAoB;;;;;;kFAInC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAa;;;;;;0FAC7B,8OAAC;gFAAK,WAAU;0FAAoB;;;;;;;;;;;;kFAEtC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAM;;;;;;0FACtB,8OAAC;gFAAK,WAAU;0FAAM;;;;;;0FACtB,8OAAC;gFAAK,WAAU;0FAAM;;;;;;;;;;;;kFAExB,8OAAC;wEAAI,WAAU;kFAAmB;;;;;;;;;;;;;;;;;;kEAItC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiB;;;;;;kFAChC,8OAAC;wEAAI,WAAU;kFAAoB;;;;;;kFAGnC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAa;;;;;;0FAC7B,8OAAC;gFAAK,WAAU;0FAAsB;;;;;;;;;;;;kFAExC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAM;;;;;;0FACtB,8OAAC;gFAAK,WAAU;0FAAM;;;;;;;;;;;;kFAExB,8OAAC;wEAAI,WAAU;kFAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCASP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAG5C,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAC5C,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;sEAIhD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;;;;;;;8DAK3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO;gEACL,OAAO;gEACP,cAAc;gEACd,UAAU;4DACZ;sEACD;;;;;;sEAGD,8OAAC;4DACC,OAAO;gEACL,OAAO;gEACP,UAAU;gEACV,YAAY;gEACZ,cAAc;4DAChB;sEACD;;;;;;sEAKD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFAGvC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,8OAAC;wEAAI,WAAU;kFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCASP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAkB,UAAU;;8CAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;8DAKZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAC/B,8OAAC;wEAAI,WAAU;kFAAc;;;;;;;;;;;;0EAE/B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;0EAML,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjB", "debugId": null}}, {"offset": {"line": 2583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/CTASection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef, useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function CTASection() {\n  const tCTA = useTranslations(\"CTA\");\n  const tCommon = useTranslations(\"Common\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [email, setEmail] = useState(\"\");\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (email) {\n      // Simulate form submission\n      setIsSubmitted(true);\n      setEmail(\"\");\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\" as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"cta\" ref={ref}>\n      <div className=\"container\">\n        <motion.div\n          className=\"cta-content\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          <h2 className=\"cta-title\">{tCTA(\"title\")}</h2>\n          <p className=\"cta-subtitle\">{tCTA(\"subtitle\")}</p>\n\n          {!isSubmitted ? (\n            <form className=\"cta-form\" onSubmit={handleSubmit}>\n              <div className=\"form-group\">\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder={tCTA(\"emailPlaceholder\")}\n                  className=\"email-input\"\n                  required\n                />\n                <button type=\"submit\" className=\"btn-primary btn-large\">\n                  {tCommon(\"getEarlyAccess\")}\n                </button>\n              </div>\n              <p className=\"form-note\">{tCTA(\"note\")}</p>\n            </form>\n          ) : (\n            <div className=\"success-message\">\n              <h3>{tCTA(\"successTitle\")}</h3>\n              <p>{tCTA(\"successMessage\")}</p>\n            </div>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,OAAO,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC7B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,OAAO;YACT,2BAA2B;YAC3B,eAAe;YACf,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAM,KAAK;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,WAAW,YAAY;;kCAEhC,8OAAC;wBAAG,WAAU;kCAAa,KAAK;;;;;;kCAChC,8OAAC;wBAAE,WAAU;kCAAgB,KAAK;;;;;;oBAEjC,CAAC,4BACA,8OAAC;wBAAK,WAAU;wBAAW,UAAU;;0CACnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAa,KAAK;wCAClB,WAAU;wCACV,QAAQ;;;;;;kDAEV,8OAAC;wCAAO,MAAK;wCAAS,WAAU;kDAC7B,QAAQ;;;;;;;;;;;;0CAGb,8OAAC;gCAAE,WAAU;0CAAa,KAAK;;;;;;;;;;;6CAGjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI,KAAK;;;;;;0CACV,8OAAC;0CAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB", "debugId": null}}, {"offset": {"line": 2747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/ScrollToCTA.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\nexport default function ScrollToCTA() {\n  useEffect(() => {\n    const handleScrollToCTA = (e: Event) => {\n      const target = e.target as HTMLElement;\n      \n      // Check if the clicked element is a CTA button (but not in a form)\n      if (\n        target.classList.contains('btn-primary') &&\n        (target.textContent?.includes('Google') || target.textContent?.includes('Early Access')) &&\n        !target.closest('form')\n      ) {\n        e.preventDefault();\n        \n        // Find the CTA section and scroll to it\n        const ctaSection = document.querySelector('.cta');\n        if (ctaSection) {\n          ctaSection.scrollIntoView({ \n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add event listener to document\n    document.addEventListener('click', handleScrollToCTA);\n\n    // Cleanup\n    return () => {\n      document.removeEventListener('click', handleScrollToCTA);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,MAAM,SAAS,EAAE,MAAM;YAEvB,mEAAmE;YACnE,IACE,OAAO,SAAS,CAAC,QAAQ,CAAC,kBAC1B,CAAC,OAAO,WAAW,EAAE,SAAS,aAAa,OAAO,WAAW,EAAE,SAAS,eAAe,KACvF,CAAC,OAAO,OAAO,CAAC,SAChB;gBACA,EAAE,cAAc;gBAEhB,wCAAwC;gBACxC,MAAM,aAAa,SAAS,aAAa,CAAC;gBAC1C,IAAI,YAAY;oBACd,WAAW,cAAc,CAAC;wBACxB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,iCAAiC;QACjC,SAAS,gBAAgB,CAAC,SAAS;QAEnC,UAAU;QACV,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD", "debugId": null}}]}