"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function ScreenshotsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="screenshots" ref={ref}>
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            See PersonaRoll <span className="gradient-text">In Action</span>
          </h2>
          <p className="section-subtitle">
            A glimpse into your new content creation workflow
          </p>
        </div>

        <motion.div
          className="screenshots-showcase"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Screenshot 1: Photo Upload */}
          <motion.div className="screenshot-item" variants={itemVariants}>
            <div className="screenshot-image">
              <div className="demo-camera-roll">
                {/* Header with title and filter tabs */}
                <div className="camera-header">
                  <div className="camera-title-section">
                    <h4 className="camera-title">Camera Roll Analyzer</h4>
                    <span className="camera-tip">
                      AI analyzes photos for trending content
                    </span>
                  </div>
                  <div className="camera-filter-tabs">
                    <div className="filter-tab active">All (24)</div>
                    <div className="filter-tab">Fashion (8)</div>
                    <div className="filter-tab">Lifestyle (6)</div>
                    <div className="filter-tab">Food (4)</div>
                    <div className="filter-tab">Travel (6)</div>
                  </div>
                </div>

                {/* Photo Grid */}
                <div className="real-photo-grid">
                  <div className="real-photo-card selected">
                    <div className="photo-image-container">
                      <div className="photo-placeholder fashion"></div>
                      <div className="delete-button">🗑️</div>
                    </div>
                    <div className="photo-info">
                      <div className="photo-filename">
                        autumn_outfit_2024.jpg
                      </div>
                      <div className="photo-description">
                        Cozy autumn sweater with matching accessories, perfect
                        for fall weather trends
                      </div>
                      <div className="photo-meta">
                        <span className="photo-date">Oct 28, 2024</span>
                        <span className="photo-status used">Used</span>
                      </div>
                      <div className="photo-tags">
                        <span className="tag">autumn</span>
                        <span className="tag">fashion</span>
                        <span className="tag">cozy</span>
                      </div>
                      <div className="photo-type-badge">Fashion</div>
                    </div>
                  </div>

                  <div className="real-photo-card">
                    <div className="photo-image-container">
                      <div className="photo-placeholder lifestyle"></div>
                    </div>
                    <div className="photo-info">
                      <div className="photo-filename">morning_coffee.jpg</div>
                      <div className="photo-description">
                        Perfect morning coffee setup with natural lighting
                      </div>
                      <div className="photo-meta">
                        <span className="photo-date">Oct 27, 2024</span>
                        <span className="photo-status unused">Unused</span>
                      </div>
                      <div className="photo-tags">
                        <span className="tag">coffee</span>
                        <span className="tag">morning</span>
                      </div>
                      <div className="photo-type-badge">Lifestyle</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>Start with Your Photos</h3>
              <p>
                Upload any photo and watch AI instantly match it with trending
                topics. Your visuals drive the content, not the other way
                around.
              </p>
            </div>
          </motion.div>

          {/* Screenshot 2: Persona Selection */}
          <motion.div
            className="screenshot-item reverse"
            variants={itemVariants}
          >
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-persona-manager-compact">
                  <div className="persona-grid">
                    <div className="persona-card active">
                      <div className="persona-card-avatar fiona">F</div>
                      <h4>Fashion Fiona</h4>
                      <p>ENFP • Fashion & Lifestyle</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini active">
                          Fashion
                        </div>
                        <div className="knowledge-chip-mini active">Style</div>
                        <div className="knowledge-chip-mini active">Trends</div>
                      </div>
                    </div>

                    <div className="persona-card">
                      <div className="persona-card-avatar marcus">M</div>
                      <h4>Chef Marcus</h4>
                      <p>ISFJ • Culinary Expert</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini">Recipes</div>
                        <div className="knowledge-chip-mini">Cooking</div>
                        <div className="knowledge-chip-mini">Food</div>
                      </div>
                    </div>
                  </div>

                  <div className="persona-detail-summary">
                    <h4
                      style={{
                        color: "var(--text-primary)",
                        marginBottom: "12px",
                        fontSize: "16px",
                      }}
                    >
                      Selected: Fashion Fiona
                    </h4>
                    <p
                      style={{
                        color: "var(--text-secondary)",
                        fontSize: "14px",
                        lineHeight: "1.5",
                        marginBottom: "16px",
                      }}
                    >
                      A fashion-forward influencer who stays ahead of trends and
                      creates authentic style content. Reads fashion feeds and
                      writes with trendy enthusiasm.
                    </p>
                    <div className="knowledge-sources-summary">
                      <div className="knowledge-chips">
                        <div className="knowledge-chip active">Vogue RSS</div>
                        <div className="knowledge-chip active">
                          Fashion Week
                        </div>
                        <div className="knowledge-chip active">Style Blogs</div>
                        <div className="knowledge-chip">Beauty Tips</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>Personas with Custom Knowledge</h3>
              <p>
                Each persona reads different sources and writes in their unique
                style. Fashion Fiona follows fashion feeds, while Foodie Fred
                reads culinary blogs.
              </p>
            </div>
          </motion.div>

          {/* Screenshot 3: AI Generation Process */}
          <motion.div className="screenshot-item" variants={itemVariants}>
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-content-generation-compact">
                  <div className="generation-process">
                    <div className="process-steps">
                      <div className="step-item active">
                        <div className="step-icon">📸</div>
                        <span>Photo Analysis</span>
                      </div>
                      <div className="step-arrow">→</div>
                      <div className="step-item active">
                        <div className="step-icon">📈</div>
                        <span>Trend Match</span>
                      </div>
                      <div className="step-arrow">→</div>
                      <div className="step-item active">
                        <div className="step-icon">✨</div>
                        <span>Generate</span>
                      </div>
                    </div>
                  </div>

                  <div className="generated-content-preview">
                    <div className="content-example">
                      <div className="content-header">
                        <div className="persona-badge">Fashion Fiona</div>
                        <div className="trend-badge">Autumn Trends</div>
                      </div>
                      <div className="content-body">
                        <p>
                          "OMG this autumn look is EVERYTHING! 😍 The oversized
                          knit + ankle boots combo is absolutely perfect for the
                          cozy fall vibes we're all craving right now!"
                        </p>
                      </div>
                      <div className="content-platforms">
                        <span className="platform-icon">📱</span>
                        <span className="platform-icon">📷</span>
                        <span className="platform-icon">🐦</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>Smart Content Generation</h3>
              <p>
                AI analyzes your photo, matches it with current trends, then
                generates content in your chosen persona's unique voice and
                style.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
