"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
  };

  return (
    <section className="features" ref={ref}>
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">
            The Complete <span className="gradient-text">Photo-to-Post</span>{" "}
            Workflow
          </h2>
          <p className="section-subtitle">
            From your camera roll to viral content in three simple steps
          </p>
        </div>

        <motion.div
          className="features-grid"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Camera Roll Feature - PRIMARY */}
          <motion.div
            className="feature-card featured primary"
            variants={cardVariants}
          >
            <div className="feature-icon">
              <div className="icon-camera">
                <div className="camera-lens"></div>
                <div className="camera-flash"></div>
              </div>
            </div>
            <h3 className="feature-title">Step 1: Upload Your Photos</h3>
            <p className="feature-description">
              Start with any photo from your camera roll. Our AI instantly
              analyzes the image and matches it with current trending topics
              across social platforms.
            </p>
            <ul className="feature-list">
              <li>Upload photos from any moment</li>
              <li>AI matches photos to current trends</li>
              <li>Your visuals drive the content strategy</li>
            </ul>
          </motion.div>

          {/* Persona Feature */}
          <motion.div className="feature-card featured" variants={cardVariants}>
            <div className="feature-icon">
              <div className="icon-personas">
                <div className="mini-persona p1"></div>
                <div className="mini-persona p2"></div>
                <div className="mini-persona p3"></div>
              </div>
            </div>
            <h3 className="feature-title">Step 2: Choose Your Voice</h3>
            <p className="feature-description">
              Select which persona will write your content. Each has their own
              personality, reads different sources, and writes in a unique style
              that stays consistent across all posts.
            </p>
            <ul className="feature-list">
              <li>Multiple AI personalities to choose from</li>
              <li>Each reads custom knowledge sources</li>
              <li>Maintains consistent voice and tone</li>
            </ul>
          </motion.div>

          {/* Trend Detection */}
          <motion.div className="feature-card" variants={cardVariants}>
            <div className="feature-icon">
              <div className="icon-trend">
                <div className="trend-line line-1"></div>
                <div className="trend-line line-2"></div>
                <div className="trend-line line-3"></div>
                <div className="trend-dot dot-1"></div>
                <div className="trend-dot dot-2"></div>
                <div className="trend-dot dot-3"></div>
              </div>
            </div>
            <h3 className="feature-title">Step 3: Get Your Post</h3>
            <p className="feature-description">
              Watch as AI combines your photo, trending topics, and persona
              knowledge to create authentic content that sounds exactly like you
              would write it.
            </p>
          </motion.div>

          {/* AI Knowledge Integration */}
          <motion.div className="feature-card" variants={cardVariants}>
            <div className="feature-icon">
              <div className="icon-knowledge">
                <div className="knowledge-sources">
                  <div className="source-node doc"></div>
                  <div className="source-node link"></div>
                  <div className="source-node feed"></div>
                  <div className="knowledge-center"></div>
                  <div className="knowledge-flow flow-1"></div>
                  <div className="knowledge-flow flow-2"></div>
                  <div className="knowledge-flow flow-3"></div>
                </div>
              </div>
            </div>
            <h3 className="feature-title">Smart Knowledge Sources</h3>
            <p className="feature-description">
              Your personas don't just write - they read. Connect knowledge
              bases, RSS feeds, and social media sources to keep your content
              informed and relevant.
            </p>
          </motion.div>

          {/* Trending Topics */}
          <motion.div className="feature-card" variants={cardVariants}>
            <div className="feature-icon">
              <div className="icon-viral">
                <div className="viral-bubble bubble-1"></div>
                <div className="viral-bubble bubble-2"></div>
                <div className="viral-bubble bubble-3"></div>
                <div className="viral-pulse"></div>
              </div>
            </div>
            <h3 className="feature-title">Always On-Trend</h3>
            <p className="feature-description">
              Real-time trend detection ensures your content matches what's hot
              right now. Never miss a viral moment or trending conversation.
            </p>
          </motion.div>

          {/* Analytics */}
          <motion.div className="feature-card" variants={cardVariants}>
            <div className="feature-icon">
              <div className="icon-analytics">
                <div className="chart-bar bar-1"></div>
                <div className="chart-bar bar-2"></div>
                <div className="chart-bar bar-3"></div>
                <div className="chart-bar bar-4"></div>
                <div className="chart-line"></div>
              </div>
            </div>
            <h3 className="feature-title">Performance Insights</h3>
            <p className="feature-description">
              Track what works across all platforms. See which photos, personas,
              and topics drive the most engagement to optimize your strategy.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
